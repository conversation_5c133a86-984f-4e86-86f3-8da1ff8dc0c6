from tkinter import filedialog, messagebox
import threading
try:
    import Tkinter as tk
except ImportError:
    import tkinter as tk

try:
    import ttk
    py3 = False
except ImportError:
    import tkinter.ttk as ttk
    py3 = True

import VideoSplitting_support
import subprocess
import os
import re
import queue
import time

def center_window(window, width, height):
    """Center the window on the screen."""
    screen_width = window.winfo_screenwidth()
    screen_height = window.winfo_screenheight()
    x = (screen_width // 2) - (width // 2)
    y = (screen_height // 2) - (height // 2)
    window.geometry(f'{width}x{height}+{x}+{y}')

def vp_start_gui():
    global val, w, root
    root = tk.Tk()
    top = Toplevel1 (root)
    VideoSplitting_support.init(root, top)
    center_window(root, 700, 320)
    root.mainloop()

w = None
def create_Toplevel1(root, *args, **kwargs):
    global w, w_win, rt
    rt = root
    w = tk.Toplevel (root)
    top = Toplevel1 (w)
    VideoSplitting_support.init(w, top, *args, **kwargs)
    return (w, top)

def destroy_Toplevel1():
    global w
    w.destroy()
    w = None

class Toplevel1:
    def __init__(self, top=None):
        _bgcolor = '#d9d9d9'  # X11 color: 'gray85'
        _fgcolor = '#000000'  # X11 color: 'black'
        _compcolor = '#d9d9d9' # X11 color: 'gray85'
        _ana1color = '#d9d9d9' # X11 color: 'gray85'
        _ana2color = '#ececec' # Closest X11 color: 'gray92'

        top.geometry("700x320")
        top.title("Video Splitting")
        top.configure(background="#d9d9d9")
        top.configure(highlightbackground="#d9d9d9")
        top.configure(highlightcolor="black")

        self.Label1 = tk.Label(top)
        self.Label1.place(relx=0.033, rely=0.086, height=23, width=37)
        self.Label1.configure(activebackground="#f9f9f9")
        self.Label1.configure(activeforeground="black")
        self.Label1.configure(background="#d9d9d9")
        self.Label1.configure(disabledforeground="#a3a3a3")
        self.Label1.configure(foreground="#000000")
        self.Label1.configure(highlightbackground="#d9d9d9")
        self.Label1.configure(highlightcolor="black")
        self.Label1.configure(text='''路径''')

        self.Text1 = tk.Text(top)
        self.Text1.place(relx=0.133, rely=0.086, relheight=0.094, relwidth=0.823)
        self.Text1.configure(background="white")
        self.Text1.configure(font="TkTextFont")
        self.Text1.configure(foreground="black")
        self.Text1.configure(highlightbackground="#d9d9d9")
        self.Text1.configure(highlightcolor="black")
        self.Text1.configure(insertbackground="black")
        self.Text1.configure(selectbackground="#c4c4c4")
        self.Text1.configure(selectforeground="black")
        self.Text1.configure(width=494)
        self.Text1.configure(wrap='word')

        self.Button1 = tk.Button(top)
        self.Button1.place(relx=0.133, rely=0.258, height=28, width=75)
        self.Button1.configure(activebackground="#ececec")
        self.Button1.configure(activeforeground="#000000")
        self.Button1.configure(background="#d9d9d9")
        self.Button1.configure(disabledforeground="#a3a3a3")
        self.Button1.configure(foreground="#000000")
        self.Button1.configure(highlightbackground="#d9d9d9")
        self.Button1.configure(highlightcolor="black")
        self.Button1.configure(pady="0")
        self.Button1.configure(text='''读取文件''')
        self.Button1.config(command=self.load_file)

        self.Text2 = tk.Text(top)
        self.Text2.place(relx=0.133, rely=0.515, relheight=0.094, relwidth=0.157)
        self.Text2.configure(background="white")
        self.Text2.configure(font="TkTextFont")
        self.Text2.configure(foreground="black")
        self.Text2.configure(highlightbackground="#d9d9d9")
        self.Text2.configure(highlightcolor="black")
        self.Text2.configure(insertbackground="black")
        self.Text2.configure(selectbackground="#c4c4c4")
        self.Text2.configure(selectforeground="black")
        self.Text2.insert(tk.END, '00:00:00')  # Set default time
        self.Text2.configure(width=94)
        self.Text2.configure(wrap='word')

        self.Text3 = tk.Text(top)
        self.Text3.place(relx=0.4, rely=0.515, relheight=0.094, relwidth=0.157)
        self.Text3.configure(background="white")
        self.Text3.configure(font="TkTextFont")
        self.Text3.configure(foreground="black")
        self.Text3.configure(highlightbackground="#d9d9d9")
        self.Text3.configure(highlightcolor="black")
        self.Text3.configure(insertbackground="black")
        self.Text3.configure(selectbackground="#c4c4c4")
        self.Text3.configure(selectforeground="black")
        self.Text3.insert(tk.END, '00:00:00')  # Set default time
        self.Text3.configure(width=94)
        self.Text3.configure(wrap='word')

        self.Label2 = tk.Label(top)
        self.Label2.place(relx=0.033, rely=0.515, height=23, width=37)
        self.Label2.configure(activebackground="#f9f9f9")
        self.Label2.configure(activeforeground="black")
        self.Label2.configure(background="#d9d9d9")
        self.Label2.configure(disabledforeground="#a3a3a3")
        self.Label2.configure(foreground="#000000")
        self.Label2.configure(highlightbackground="#d9d9d9")
        self.Label2.configure(highlightcolor="black")
        self.Label2.configure(text='''开始''')

        self.Label3 = tk.Label(top)
        self.Label3.place(relx=0.317, rely=0.515, height=23, width=37)
        self.Label3.configure(activebackground="#f9f9f9")
        self.Label3.configure(activeforeground="black")
        self.Label3.configure(background="#d9d9d9")
        self.Label3.configure(disabledforeground="#a3a3a3")
        self.Label3.configure(foreground="#000000")
        self.Label3.configure(highlightbackground="#d9d9d9")
        self.Label3.configure(highlightcolor="black")
        self.Label3.configure(text='''结束''')

        self.Button2 = tk.Button(top)
        self.Button2.place(relx=0.55, rely=0.72, height=28, width=75)
        self.Button2.configure(activebackground="#ececec")
        self.Button2.configure(activeforeground="#000000")
        self.Button2.configure(background="#d9d9d9")
        self.Button2.configure(disabledforeground="#a3a3a3")
        self.Button2.configure(foreground="#000000")
        self.Button2.configure(highlightbackground="#d9d9d9")
        self.Button2.configure(highlightcolor="black")
        self.Button2.configure(pady="0")
        self.Button2.configure(text='''开始分割''')
        self.Button2.config(command=self.split_media)

        # 添加处理方式选择
        self.Label4 = tk.Label(top)
        self.Label4.place(relx=0.033, rely=0.65, height=23, width=60)
        self.Label4.configure(activebackground="#f9f9f9")
        self.Label4.configure(activeforeground="black")
        self.Label4.configure(background="#d9d9d9")
        self.Label4.configure(disabledforeground="#a3a3a3")
        self.Label4.configure(foreground="#000000")
        self.Label4.configure(highlightbackground="#d9d9d9")
        self.Label4.configure(highlightcolor="black")
        self.Label4.configure(text='''处理方式''')

        # 处理方式选择：0=GPU, 1=CPU, 2=快速复制
        self.process_mode = tk.IntVar()
        self.process_mode.set(0)  # 默认使用GPU模式

        # GPU加速模式
        self.Radio1 = tk.Radiobutton(top)
        self.Radio1.place(relx=0.15, rely=0.65, height=23, width=200)
        self.Radio1.configure(activebackground="#ececec")
        self.Radio1.configure(activeforeground="#000000")
        self.Radio1.configure(background="#d9d9d9")
        self.Radio1.configure(disabledforeground="#a3a3a3")
        self.Radio1.configure(foreground="#000000")
        self.Radio1.configure(highlightbackground="#d9d9d9")
        self.Radio1.configure(highlightcolor="black")
        self.Radio1.configure(text='''GPU加速模式 (推荐)''')
        self.Radio1.configure(variable=self.process_mode)
        self.Radio1.configure(value=0)

        # CPU模式
        self.Radio2 = tk.Radiobutton(top)
        self.Radio2.place(relx=0.15, rely=0.72, height=23, width=200)
        self.Radio2.configure(activebackground="#ececec")
        self.Radio2.configure(activeforeground="#000000")
        self.Radio2.configure(background="#d9d9d9")
        self.Radio2.configure(disabledforeground="#a3a3a3")
        self.Radio2.configure(foreground="#000000")
        self.Radio2.configure(highlightbackground="#d9d9d9")
        self.Radio2.configure(highlightcolor="black")
        self.Radio2.configure(text='''CPU模式''')
        self.Radio2.configure(variable=self.process_mode)
        self.Radio2.configure(value=1)

        # 快速复制模式
        self.Radio3 = tk.Radiobutton(top)
        self.Radio3.place(relx=0.15, rely=0.79, height=23, width=200)
        self.Radio3.configure(activebackground="#ececec")
        self.Radio3.configure(activeforeground="#000000")
        self.Radio3.configure(background="#d9d9d9")
        self.Radio3.configure(disabledforeground="#a3a3a3")
        self.Radio3.configure(foreground="#000000")
        self.Radio3.configure(highlightbackground="#d9d9d9")
        self.Radio3.configure(highlightcolor="black")
        self.Radio3.configure(text='''快速复制模式''')
        self.Radio3.configure(variable=self.process_mode)
        self.Radio3.configure(value=2)

        # 添加进度标签
        self.progress_label = tk.Label(top)
        self.progress_label.place(relx=0.133, rely=0.85, height=20, width=400)
        self.progress_label.configure(background="#d9d9d9")
        self.progress_label.configure(foreground="#000000")
        self.progress_label.configure(text="")
        self.progress_label.place_forget()  # 初始隐藏

        # 添加进度条
        self.progressbar = ttk.Progressbar(top)
        self.progressbar.place(relx=0.133, rely=0.88, relwidth=0.7, height=20)
        self.progressbar.configure(mode='determinate')
        self.progressbar.configure(maximum=100)
        self.progressbar.place_forget()  # 初始隐藏进度条

        # 进度队列用于线程间通信
        self.progress_queue = queue.Queue()
        self.total_frames = 0
        self.last_error = ""

    def get_video_duration(self, file_path):
        '''获取视频时长'''
        try:
            command = f'ffprobe -v quiet -show_entries format=duration -of csv="p=0" "{file_path}"'
            result = subprocess.run(command, shell=True, capture_output=True, text=True)
            if result.returncode == 0:
                duration_seconds = float(result.stdout.strip())
                hours = int(duration_seconds // 3600)
                minutes = int((duration_seconds % 3600) // 60)
                seconds = int(duration_seconds % 60)
                return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
        except Exception as e:
            print(f"获取视频时长失败: {e}")
        return None

    def get_video_frame_count(self, file_path, start_time, end_time):
        '''获取视频片段的总帧数'''
        try:
            # 计算时长（秒）
            start_seconds = self.time_to_seconds(start_time)
            end_seconds = self.time_to_seconds(end_time)
            duration = end_seconds - start_seconds

            # 获取帧率
            command = f'ffprobe -v quiet -select_streams v:0 -show_entries stream=r_frame_rate -of csv="p=0" "{file_path}"'
            result = subprocess.run(command, shell=True, capture_output=True, text=True)
            if result.returncode == 0:
                fps_str = result.stdout.strip()
                if '/' in fps_str:
                    num, den = fps_str.split('/')
                    fps = float(num) / float(den)
                else:
                    fps = float(fps_str)

                # 计算总帧数
                total_frames = int(duration * fps)
                return total_frames
        except Exception as e:
            print(f"获取视频帧数失败: {e}")
        return None

    def time_to_seconds(self, time_str):
        '''将时间字符串转换为秒数'''
        try:
            parts = time_str.split(':')
            hours = int(parts[0])
            minutes = int(parts[1])
            seconds = float(parts[2])
            return hours * 3600 + minutes * 60 + seconds
        except:
            return 0

    def check_gpu_support(self):
        '''检查GPU编码器支持情况'''
        gpu_support = {
            'nvidia': False,
            'amd': False,
            'intel': False
        }

        try:
            # 检查NVIDIA GPU支持
            result = subprocess.run('ffmpeg -hide_banner -encoders 2>&1 | findstr nvenc',
                                  shell=True, capture_output=True, text=True)
            if result.returncode == 0 and 'h264_nvenc' in result.stdout:
                gpu_support['nvidia'] = True

            # 检查AMD GPU支持
            result = subprocess.run('ffmpeg -hide_banner -encoders 2>&1 | findstr amf',
                                  shell=True, capture_output=True, text=True)
            if result.returncode == 0 and 'h264_amf' in result.stdout:
                gpu_support['amd'] = True

            # 检查Intel GPU支持
            result = subprocess.run('ffmpeg -hide_banner -encoders 2>&1 | findstr qsv',
                                  shell=True, capture_output=True, text=True)
            if result.returncode == 0 and 'h264_qsv' in result.stdout:
                gpu_support['intel'] = True

        except Exception as e:
            print(f"检查GPU支持时出错: {e}")

        return gpu_support

    def load_file(self):
        '''Load file and display its path in Text1'''
        filename = filedialog.askopenfilename()
        if filename:
            self.Text1.delete(1.0, tk.END)
            self.Text1.insert(tk.END, filename)

            # 自动获取视频时长并填写到时间框
            duration = self.get_video_duration(filename)
            if duration:
                # 设置开始时间为00:00:00
                self.Text2.delete(1.0, tk.END)
                self.Text2.insert(tk.END, '00:00:00')

                # 设置结束时间为视频总时长
                self.Text3.delete(1.0, tk.END)
                self.Text3.insert(tk.END, duration)

                messagebox.showinfo("视频信息", f"视频时长: {duration}\n已自动填写起始和结束时间\n\n处理方式说明:\n• GPU加速模式: 使用显卡加速，速度快且画面流畅\n• CPU模式: 使用处理器编码，兼容性好但速度较慢\n• 快速复制模式: 直接复制数据，最快但可能有画面延迟")
            else:
                messagebox.showwarning("警告", "无法获取视频时长，请手动填写时间")

    def start_progress(self, show_label=True):
        '''显示并启动进度条'''
        if show_label:
            self.progress_label.place(relx=0.133, rely=0.85, height=20, width=400)
        self.progressbar.place(relx=0.133, rely=0.88, relwidth=0.7, height=20)
        self.progressbar['value'] = 0

    def stop_progress(self):
        '''停止并隐藏进度条'''
        self.progressbar.place_forget()
        self.progress_label.place_forget()

    def update_progress(self, current_frame, total_frames, status=""):
        '''更新进度显示'''
        if total_frames > 0:
            progress = (current_frame / total_frames) * 100
            self.progressbar['value'] = progress

            # 更新标签文字
            label_text = f"{status} - {current_frame}/{total_frames}帧 ({progress:.1f}%)"
            self.progress_label.config(text=label_text)

    def check_progress_queue(self):
        '''检查进度队列并更新UI'''
        try:
            while True:
                progress_data = self.progress_queue.get_nowait()
                if progress_data['type'] == 'progress':
                    self.update_progress(progress_data['current'], progress_data['total'], progress_data['status'])
                elif progress_data['type'] == 'status':
                    self.progress_label.config(text=progress_data['message'])
        except queue.Empty:
            pass

        # 继续检查队列
        if hasattr(self, '_progress_check_id'):
            self.progress_label.after(100, self.check_progress_queue)

    def run_ffmpeg_with_progress(self, command, total_frames, status="处理中"):
        '''运行ffmpeg并实时显示进度'''
        try:
            print(f"执行命令: {command}")
            process = subprocess.Popen(command, shell=True, stderr=subprocess.PIPE,
                                     stdout=subprocess.PIPE, universal_newlines=True, bufsize=1)

            frame_pattern = re.compile(r'frame=\s*(\d+)')
            error_output = []

            for line in process.stderr:
                error_output.append(line)
                print(f"ffmpeg输出: {line.strip()}")  # 调试信息

                # 解析当前帧数
                match = frame_pattern.search(line)
                if match:
                    current_frame = int(match.group(1))
                    # 将进度信息放入队列
                    self.progress_queue.put({
                        'type': 'progress',
                        'current': current_frame,
                        'total': total_frames,
                        'status': status
                    })

            process.wait()

            # 如果失败，保存错误信息
            if process.returncode != 0:
                self.last_error = ''.join(error_output)
                print(f"ffmpeg失败，返回码: {process.returncode}")
                print(f"错误信息: {self.last_error}")

            return process.returncode

        except Exception as e:
            print(f"执行ffmpeg时出错: {e}")
            self.last_error = str(e)
            return 1

    def split_media(self):
        '''Split media based on start and end times'''
        self.Button2.config(state=tk.DISABLED, text='分割中')  # 设置按钮为不可点击并更改文本
        self.start_progress()  # 显示进度条

        # 启动进度检查
        self._progress_check_id = True
        self.check_progress_queue()

        thread = threading.Thread(target=self._split_media)
        thread.start()

    def _split_media(self):
        '''Internal method to handle the actual media splitting process.'''
        file_path = self.Text1.get(1.0, tk.END).strip()
        start_time = self.Text2.get(1.0, tk.END).strip()
        end_time = self.Text3.get(1.0, tk.END).strip()
        if file_path and start_time and end_time:
            extension = os.path.splitext(file_path)[1].lower()
            base_name = os.path.splitext(os.path.basename(file_path))[0]
            output_dir = os.path.dirname(file_path)

            # 根据用户选择的处理方式生成命令
            mode = self.process_mode.get()

            # 获取视频帧数用于进度显示
            self.progress_queue.put({
                'type': 'status',
                'message': '正在分析视频信息...'
            })

            self.total_frames = self.get_video_frame_count(file_path, start_time, end_time)
            if not self.total_frames:
                self.total_frames = 1000  # 默认值，避免除零错误

            # 检查GPU支持情况
            gpu_support = self.check_gpu_support()
            print(f"GPU支持情况: {gpu_support}")

            if mode == 0:  # GPU加速模式
                output_file = os.path.join(output_dir, f"{base_name}_split_gpu{extension}")
                # 根据支持情况生成GPU命令
                gpu_commands = []

                if gpu_support['nvidia']:
                    gpu_commands.append(f'ffmpeg -y -hwaccel cuda -i "{file_path}" -ss {start_time} -to {end_time} -c:v h264_nvenc -preset fast -c:a aac -avoid_negative_ts make_zero "{output_file}"')

                if gpu_support['amd']:
                    gpu_commands.append(f'ffmpeg -y -hwaccel d3d11va -i "{file_path}" -ss {start_time} -to {end_time} -c:v h264_amf -c:a aac -avoid_negative_ts make_zero "{output_file}"')

                if gpu_support['intel']:
                    gpu_commands.append(f'ffmpeg -y -hwaccel qsv -i "{file_path}" -ss {start_time} -to {end_time} -c:v h264_qsv -c:a aac -avoid_negative_ts make_zero "{output_file}"')

                # 如果没有GPU支持，直接使用CPU
                if not gpu_commands:
                    gpu_commands = []

                fallback_command = f'ffmpeg -y -i "{file_path}" -ss {start_time} -to {end_time} -c:v libx264 -preset fast -c:a aac -avoid_negative_ts make_zero "{output_file}"'
                command = gpu_commands
            elif mode == 1:  # CPU模式
                output_file = os.path.join(output_dir, f"{base_name}_split_cpu{extension}")
                # CPU编码：直接使用libx264
                command = f'ffmpeg -y -i "{file_path}" -ss {start_time} -to {end_time} -c:v libx264 -preset fast -c:a aac -avoid_negative_ts make_zero "{output_file}"'
                fallback_command = None
            else:  # 快速复制模式
                output_file = os.path.join(output_dir, f"{base_name}_split_fast{extension}")
                # 快速复制：直接复制数据流
                command = f'ffmpeg -y -ss {start_time} -i "{file_path}" -to {end_time} -c copy -avoid_negative_ts make_zero "{output_file}"'
                fallback_command = None

            # 执行命令
            result = None
            mode_names = ["GPU加速", "CPU", "快速复制"]

            if mode == 0:  # GPU加速模式
                result = None

                if isinstance(command, list) and command:
                    # 有可用的GPU命令
                    print("尝试GPU硬件加速...")
                    gpu_types = []
                    if gpu_support['nvidia']: gpu_types.append("NVIDIA")
                    if gpu_support['amd']: gpu_types.append("AMD")
                    if gpu_support['intel']: gpu_types.append("Intel")

                    for i, gpu_cmd in enumerate(command):
                        gpu_type = gpu_types[i] if i < len(gpu_types) else f"GPU{i+1}"
                        print(f"尝试{gpu_type} GPU: {gpu_cmd}")
                        self.progress_queue.put({
                            'type': 'status',
                            'message': f'尝试{gpu_type} GPU加速...'
                        })

                        returncode = self.run_ffmpeg_with_progress(gpu_cmd, self.total_frames, f"{gpu_type} GPU")
                        if returncode == 0:
                            print(f"{gpu_type} GPU编码成功！")
                            result = type('obj', (object,), {'returncode': 0})()
                            break
                        else:
                            print(f"{gpu_type} GPU编码失败: {self.last_error}")

                # 如果没有GPU支持或所有GPU都失败，回退到CPU
                if not result or result.returncode != 0:
                    if fallback_command:
                        print("回退到CPU编码...")
                        self.progress_queue.put({
                            'type': 'status',
                            'message': '回退到CPU编码...'
                        })
                        returncode = self.run_ffmpeg_with_progress(fallback_command, self.total_frames, "CPU")
                        result = type('obj', (object,), {'returncode': returncode, 'stderr': self.last_error})()
                    else:
                        result = type('obj', (object,), {'returncode': 1, 'stderr': self.last_error})()
            else:
                # CPU模式或快速复制模式
                print(f"使用{mode_names[mode]}模式: {command}")
                if mode == 2:  # 快速复制模式，使用传统方式
                    result = subprocess.run(command, shell=True, capture_output=True)
                else:  # CPU模式，使用进度显示
                    self.progress_queue.put({
                        'type': 'status',
                        'message': f'使用{mode_names[mode]}模式处理...'
                    })
                    returncode = self.run_ffmpeg_with_progress(command, self.total_frames, mode_names[mode])
                    result = type('obj', (object,), {'returncode': returncode, 'stderr': self.last_error})()

            # 停止进度检查
            self._progress_check_id = False

            if result and result.returncode == 0:
                self.progress_queue.put({
                    'type': 'status',
                    'message': '视频分割完成！'
                })
                time.sleep(0.5)  # 让用户看到完成消息
                self.stop_progress()  # 停止进度条
                self.Button2.config(state=tk.NORMAL, text='开始分割')  # 恢复按钮状态
                messagebox.showinfo("视频分割", "视频分割完成！")
            else:
                self.stop_progress()  # 停止进度条
                self.Button2.config(state=tk.NORMAL, text='开始分割')  # 恢复按钮状态
                error_msg = "分割过程中发生错误。"
                if hasattr(result, 'stderr') and result.stderr:
                    if isinstance(result.stderr, bytes):
                        error_msg += f"\n{result.stderr.decode('utf-8', errors='ignore')}"
                    else:
                        error_msg += f"\n{result.stderr}"
                elif self.last_error:
                    error_msg += f"\n{self.last_error}"
                messagebox.showerror("错误", error_msg)
        else:
            self._progress_check_id = False
            self.stop_progress()  # 停止进度条
            self.Button2.config(state=tk.NORMAL, text='开始分割')  # 恢复按钮状态
            messagebox.showerror("错误", "请正确填写所有字段。")


if __name__ == '__main__':
    vp_start_gui()